// Client-side Whop utilities
export interface WhopUser {
  id: string;
  username: string;
  name: string;
  email?: string;
  profilePictureUrl?: string;
  bio?: string;
  city?: string;
  country?: string;
  phoneVerified?: boolean;
  bannerUrl?: string;
  createdAt: string;
}

// Get current user data from Whop (uses headers for authentication)
export async function getCurrentWhopUser(): Promise<WhopUser | null> {
  try {
    console.log('🔍 Attempting to get current Whop user...');

    // Check if we have URL parameters that might indicate Whop context
    const urlParams = new URLSearchParams(window.location.search);
    const whopUserId = urlParams.get('whop_user_id') || urlParams.get('user_id');

    if (whopUserId) {
      console.log('📋 Found Whop user ID in URL:', whopUserId);
      // Try to get user by ID
      return await getWhopUserById(whopUserId);
    }

    const response = await fetch('/api/auth/whop');

    if (!response.ok) {
      console.log('⚠️ Whop API response not OK:', response.status, response.statusText);
      return null;
    }

    const userData = await response.json();
    console.log('✅ Got Whop user data:', userData);
    return userData;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.log('⚠️ Error getting current Whop user (this is normal in development):', errorMessage);
    return null;
  }
}

// Get user data from Whop using user ID (server-side call)
export async function getWhopUserById(userId: string): Promise<WhopUser | null> {
  try {
    const response = await fetch('/api/auth/whop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });
    
    if (!response.ok) {
      console.error('Failed to get Whop user by ID:', response.statusText);
      return null;
    }
    
    const userData = await response.json();
    return userData;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error getting Whop user by ID:', errorMessage);
    return null;
  }
}

// Check if we're running in a Whop iframe
export function isInWhopIframe(): boolean {
  try {
    return window.parent !== window && window.location !== window.parent.location;
  } catch (e) {
    return true; // If we can't access parent, we're probably in an iframe
  }
}

// Get Whop context from URL parameters or iframe
export function getWhopContext(): { userId?: string; token?: string } {
  if (typeof window === 'undefined') return {};
  
  const urlParams = new URLSearchParams(window.location.search);
  const userId = urlParams.get('whop_user_id') || urlParams.get('user_id');
  const token = urlParams.get('whop_token') || urlParams.get('token');
  
  return {
    userId: userId || undefined,
    token: token || undefined,
  };
}

// Generate Whop profile URL
export function getWhopProfileUrl(userId: string): string {
  return `https://whop.com/user/${userId}`;
}
